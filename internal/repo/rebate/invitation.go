package rebate

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type InvitationRepository struct{}

func (i *InvitationRepository) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	var snapshot model.ReferralSnapshot
	err := global.GVA_DB.Debug().WithContext(ctx).
		Preload("User").
		First(&snapshot, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &snapshot, nil
}

func (i *InvitationRepository) Create(ctx context.Context, user *model.User) error {
	return global.GVA_DB.WithContext(ctx).Create(user).Error
}

func (i *InvitationRepository) CreateWallet(ctx context.Context, wallet *model.UserWallet) error {
	return global.GVA_DB.WithContext(ctx).Create(wallet).Error
}

func (i *InvitationRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.Debug().WithContext(ctx).
		First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (i *InvitationRepository) GetByIDWithSnapshot(ctx context.Context, id uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.Debug().WithContext(ctx).
		Preload("ReferralSnapshot").
		First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (i *InvitationRepository) WithTransaction(ctx context.Context, fn func(ctx context.Context) (*model.User, error)) (*model.User, error) {
	var result *model.User
	err := global.GVA_DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Replace the current repository with transaction repository temporarily
		originalDB := global.GVA_DB
		global.GVA_DB = tx
		defer func() {
			global.GVA_DB = originalDB
		}()

		var err error
		result, err = fn(ctx)
		return err
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

func (i *InvitationRepository) Update(ctx context.Context, user *model.User) error {
	return global.GVA_DB.WithContext(ctx).Save(user).Error
}

func (i *InvitationRepository) GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		First(&referral, "user_id = ? AND referrer_id = ?", userID, referrerID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

func (i *InvitationRepository) HasDirectReferral(ctx context.Context, userID uuid.UUID) (bool, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("user_id = ? AND depth = 1", userID).
		Count(&count).Error

	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (i *InvitationRepository) IsInUpline(ctx context.Context, referrerID, userID uuid.UUID) (bool, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).Model(&model.Referral{}).
		Where("user_id = ? AND referrer_id = ?", referrerID, userID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (i *InvitationRepository) GetAllReferrals(ctx context.Context, tx *gorm.DB, userID uuid.UUID) ([]model.Referral, error) {
	var referrals []model.Referral
	err := tx.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("user_id = ?", userID).
		Find(&referrals).Error
	if err != nil {
		return nil, err
	}
	return referrals, nil
}

func (i *InvitationRepository) GetByInvitationCode(ctx context.Context, code string) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).First(&user, "invitation_code = ?", code).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (i *InvitationRepository) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	return i.GetByInvitationCode(ctx, invitationCode)
}

func (i *InvitationRepository) CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error {
	return global.GVA_DB.WithContext(ctx).Create(wallet).Error
}

func (i *InvitationRepository) GetDirectInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Count(&count).Error
	return int(count), err
}

func (i *InvitationRepository) GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error) {
	query := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Joins("JOIN users ON referrals.user_id = users.id").
		Joins("LEFT JOIN affiliate_transactions ON users.id = affiliate_transactions.user_id").
		Where("referrals.referrer_id = ? AND referrals.depth = 1", userID).
		Where("affiliate_transactions.id IS NOT NULL")

	if startTime != nil {
		query = query.Where("affiliate_transactions.created_at >= ?", startTime)
	}
	if endTime != nil {
		query = query.Where("affiliate_transactions.created_at <= ?", endTime)
	}

	var count int64
	err := query.Distinct("referrals.user_id").Count(&count).Error
	return int(count), err
}

func (i *InvitationRepository) GetIndirectInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id IN (SELECT user_id FROM referrals WHERE referrer_id = ? AND depth = 1)", userID).
		Count(&count).Error
	return int(count), err
}

func (i *InvitationRepository) GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id IN (SELECT user_id FROM referrals WHERE referrer_id IN (SELECT user_id FROM referrals WHERE referrer_id = ? AND depth = 1))", userID).
		Count(&count).Error
	return int(count), err
}

func (i *InvitationRepository) GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	var addresses []string
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.UserWallet{}).
		Joins("JOIN referrals ON user_wallets.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Pluck("user_wallets.wallet_address", &addresses).Error
	return addresses, err
}

func (i *InvitationRepository) GetMemeTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	query := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Joins("JOIN referrals ON affiliate_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND affiliate_transactions.transaction_type = ?", userID, "MEME")

	if startTime != nil {
		query = query.Where("affiliate_transactions.created_at >= ?", startTime)
	}
	if endTime != nil {
		query = query.Where("affiliate_transactions.created_at <= ?", endTime)
	}

	var total float64
	err := query.Select("COALESCE(SUM(volume_usd), 0)").Scan(&total).Error
	return total, err
}

func (i *InvitationRepository) GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	query := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Joins("JOIN referrals ON affiliate_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND affiliate_transactions.transaction_type = ?", userID, "CONTRACT")

	if startTime != nil {
		query = query.Where("affiliate_transactions.created_at >= ?", startTime)
	}
	if endTime != nil {
		query = query.Where("affiliate_transactions.created_at <= ?", endTime)
	}

	var total float64
	err := query.Select("COALESCE(SUM(volume_usd), 0)").Scan(&total).Error
	return total, err
}
