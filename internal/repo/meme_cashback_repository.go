package repo

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MemeCashbackRepositoryInterface defines the interface for meme cashback operations
type MemeCashbackRepositoryInterface interface {
	CreateMemeCashback(ctx context.Context, cashback *model.MemeCashback) error
	GetMemeCashbackByID(ctx context.Context, id uuid.UUID) (*model.MemeCashback, error)
	GetMemeCashbackByTransactionID(ctx context.Context, transactionID uint) (*model.MemeCashback, error)
	GetMemeCashbacksByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.MemeCashback, error)
	GetPendingMemeCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCashback, error)
	UpdateMemeCashbackStatus(ctx context.Context, id uuid.UUID, status string) error
	MarkMemeCashbackAsClaimed(ctx context.Context, id uuid.UUID) error
	GetTotalMemeCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetPendingMemeCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
}

// MemeCashbackRepository implements the meme cashback repository interface
type MemeCashbackRepository struct {
	db *gorm.DB
}

// NewMemeCashbackRepository creates a new meme cashback repository
func NewMemeCashbackRepository() MemeCashbackRepositoryInterface {
	return &MemeCashbackRepository{
		db: global.GVA_DB,
	}
}

// CreateMemeCashback creates a new meme cashback record
func (r *MemeCashbackRepository) CreateMemeCashback(ctx context.Context, cashback *model.MemeCashback) error {
	return r.db.WithContext(ctx).Create(cashback).Error
}

// GetMemeCashbackByID retrieves a meme cashback by ID
func (r *MemeCashbackRepository) GetMemeCashbackByID(ctx context.Context, id uuid.UUID) (*model.MemeCashback, error) {
	var cashback model.MemeCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("id = ?", id).
		First(&cashback).Error
	if err != nil {
		return nil, err
	}
	return &cashback, nil
}

// GetMemeCashbackByTransactionID retrieves a meme cashback by affiliate transaction ID
func (r *MemeCashbackRepository) GetMemeCashbackByTransactionID(ctx context.Context, transactionID uint) (*model.MemeCashback, error) {
	var cashback model.MemeCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("affiliate_transaction_id = ?", transactionID).
		First(&cashback).Error
	if err != nil {
		return nil, err
	}
	return &cashback, nil
}

// GetMemeCashbacksByUserID retrieves meme cashbacks for a specific user
func (r *MemeCashbackRepository) GetMemeCashbacksByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.MemeCashback, error) {
	var cashbacks []model.MemeCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cashbacks).Error
	return cashbacks, err
}

// GetPendingMemeCashbacksByUserID retrieves pending meme cashbacks for a specific user
func (r *MemeCashbackRepository) GetPendingMemeCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCashback, error) {
	var cashbacks []model.MemeCashback
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Order("created_at ASC").
		Find(&cashbacks).Error
	return cashbacks, err
}

// UpdateMemeCashbackStatus updates the status of a meme cashback
func (r *MemeCashbackRepository) UpdateMemeCashbackStatus(ctx context.Context, id uuid.UUID, status string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.MemeCashback{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": now,
		}).Error
}

// MarkMemeCashbackAsClaimed marks a meme cashback as claimed
func (r *MemeCashbackRepository) MarkMemeCashbackAsClaimed(ctx context.Context, id uuid.UUID) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.MemeCashback{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     "CLAIMED",
			"claimed_at": now,
			"updated_at": now,
		}).Error
}

// GetTotalMemeCashbackByUserID calculates the total meme cashback amount for a user
func (r *MemeCashbackRepository) GetTotalMemeCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id = ?", userID).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalAmount, nil
}

// GetPendingMemeCashbackByUserID calculates the pending meme cashback amount for a user
func (r *MemeCashbackRepository) GetPendingMemeCashbackByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal
	}

	err := r.db.WithContext(ctx).
		Model(&model.MemeCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalAmount, nil
}
