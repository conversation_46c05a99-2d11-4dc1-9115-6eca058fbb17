package contract

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/rebate"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// ContractCommissionService 合约佣金计算服务
type ContractCommissionService struct {
	userRepo       repo.InvitationRepo
	levelRepo      repo.LevelRepo
	commissionRepo transaction.CommissionLedgerRepositoryInterface
}

// NewContractCommissionService 创建合约佣金计算服务实例
func NewContractCommissionService() *ContractCommissionService {
	return &ContractCommissionService{
		userRepo:       &rebate.InvitationRepository{},
		levelRepo:      repo.NewLevelRepository(),
		commissionRepo: transaction.NewCommissionLedgerRepository(),
	}
}

// CommissionCalculationResult 佣金计算结果
type CommissionCalculationResult struct {
	TransactionID          string                   `json:"transaction_id"`
	UserID                 uuid.UUID                `json:"user_id"`
	TransactionVolume      decimal.Decimal          `json:"transaction_volume"`
	TotalFee               decimal.Decimal          `json:"total_fee"`
	HyperliquidFee         decimal.Decimal          `json:"hyperliquid_fee"`
	XbitBuildFee           decimal.Decimal          `json:"xbit_build_fee"`
	PlatformReward         decimal.Decimal          `json:"platform_reward"`
	CommissionPool         decimal.Decimal          `json:"commission_pool"`
	DistributedCommissions []CommissionDistribution `json:"distributed_commissions"`
	RemainingToXbit        decimal.Decimal          `json:"remaining_to_xbit"`
}

// CommissionDistribution 佣金分配详情
type CommissionDistribution struct {
	RecipientUserID  uuid.UUID       `json:"recipient_user_id"`
	RecipientLevel   string          `json:"recipient_level"`
	CommissionType   string          `json:"commission_type"` // "direct", "indirect", "extended"
	CommissionRate   decimal.Decimal `json:"commission_rate"`
	CommissionAmount decimal.Decimal `json:"commission_amount"`
	Depth            int             `json:"depth"`
}

// FeeCalculation 费用计算结果
type FeeCalculation struct {
	TotalFee       decimal.Decimal `json:"total_fee"`
	HyperliquidFee decimal.Decimal `json:"hyperliquid_fee"`
	XbitBuildFee   decimal.Decimal `json:"xbit_build_fee"`
	PlatformReward decimal.Decimal `json:"platform_reward"`
	CommissionPool decimal.Decimal `json:"commission_pool"`
}

// ProcessContractCommission 处理合约交易佣金计算和分配
func (s *ContractCommissionService) ProcessContractCommission(ctx context.Context, hyperliquidTx *model.HyperLiquidTransaction) (*CommissionCalculationResult, error) {
	global.GVA_LOG.Info("Processing contract commission",
		zap.String("cloid", hyperliquidTx.Cloid),
		zap.String("user_id", hyperliquidTx.UserID.String()),
		zap.String("status", *hyperliquidTx.Status))

	// 只处理已完成的交易
	if *hyperliquidTx.Status != "filled" {
		global.GVA_LOG.Debug("Skipping non-filled transaction",
			zap.String("cloid", hyperliquidTx.Cloid),
			zap.String("status", *hyperliquidTx.Status))
		return nil, nil
	}

	var transactionVolume decimal.Decimal
	if hyperliquidTx.TotalSz != nil {
		totalSz, err := decimal.NewFromString(*hyperliquidTx.TotalSz)
		if err != nil {
			global.GVA_LOG.Debug("failed to parse totalZs",
				zap.String("cloid", hyperliquidTx.Cloid))
			return nil, nil
		}
		// 计算交易量
		transactionVolume = hyperliquidTx.AvgPrice.Mul(totalSz)
	}

	// 处理上级用户的佣金分配
	distributions, err := s.processCommissionDistribution(ctx, hyperliquidTx)
	if err != nil {
		return nil, fmt.Errorf("failed to process commission distribution: %w", err)
	}

	result := &CommissionCalculationResult{
		TransactionID:          hyperliquidTx.Cloid,
		UserID:                 *hyperliquidTx.UserID,
		TransactionVolume:      transactionVolume,
		DistributedCommissions: distributions,
	}

	global.GVA_LOG.Info("Contract commission processed successfully",
		zap.String("cloid", hyperliquidTx.Cloid),
		zap.String("transaction_volume", transactionVolume.String()))

	return result, nil
}

// getUserWithLevel gets user with their agent level information
func (s *ContractCommissionService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// processCommissionDistribution processes commission distribution to upline users
// This implements the commission matrix from the diagram
func (s *ContractCommissionService) processCommissionDistribution(ctx context.Context, tx *model.HyperLiquidTransaction) ([]CommissionDistribution, error) {
	var distributions []CommissionDistribution

	// Get the upline hierarchy for the user
	uplineUsers, err := s.getUplineHierarchy(ctx, *tx.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get upline hierarchy: %w", err)
	}

	if len(uplineUsers) == 0 {
		global.GVA_LOG.Debug("No upline users found for commission distribution",
			zap.String("user_id", tx.UserID.String()))
		return distributions, nil
	}

	// Process commission for each upline user based on the matrix
	for i, uplineUser := range uplineUsers {
		commissionRate, err := s.getCommissionRateForDepth(ctx, uplineUser, i+1)
		if err != nil {
			global.GVA_LOG.Error("Failed to get commission rate for upline user",
				zap.String("upline_user_id", uplineUser.ID.String()),
				zap.Int("depth", i+1),
				zap.Error(err))
			continue
		}

		if commissionRate.GreaterThan(decimal.Zero) {
			global.GVA_LOG.Info("Calculated commission for upline user",
				zap.String("cloid", tx.Cloid),
				zap.String("source_user_id", tx.UserID.String()),
				zap.String("upline_user_id", uplineUser.ID.String()),
				zap.Int("depth", i+1),
				zap.String("commission_rate", commissionRate.String()),
			)

			// Create commission ledger entry
			if err := s.createCommissionLedger(ctx, tx, uplineUser.ID); err != nil {
				global.GVA_LOG.Error("Failed to create commission ledger",
					zap.String("upline_user_id", uplineUser.ID.String()),
					zap.Error(err))
				continue
			}

			// Add to distributions list
			distributions = append(distributions, CommissionDistribution{
				RecipientUserID:  uplineUser.ID,
				RecipientLevel:   uplineUser.AgentLevel.Name,
				CommissionType:   s.getCommissionTypeForDepth(i + 1),
				CommissionRate:   commissionRate,
				Depth:            i + 1,
			})
		}
	}

	return distributions, nil
}

// getCommissionTypeForDepth returns the commission type for a given depth
func (s *ContractCommissionService) getCommissionTypeForDepth(depth int) string {
	switch depth {
	case 1:
		return "direct"
	case 2:
		return "indirect"
	case 3:
		return "extended"
	default:
		return "unknown"
	}
}

// getUplineHierarchy gets the upline hierarchy for a user (up to 3 levels)
func (s *ContractCommissionService) getUplineHierarchy(ctx context.Context, userID uuid.UUID) ([]model.User, error) {
	var uplineUsers []model.User

	// Get direct referrer (L1)
	referralInfo, err := s.getReferralInfo(ctx, userID)
	if err != nil {
		// No referral info found, return empty list
		return uplineUsers, nil
	}

	if referralInfo.ReferrerID == nil {
		return uplineUsers, nil
	}

	// Get L1 upline
	l1User, err := s.getUserWithLevel(ctx, *referralInfo.ReferrerID)
	if err != nil {
		return uplineUsers, nil
	}
	uplineUsers = append(uplineUsers, *l1User)

	// Get L2 upline (referrer of L1)
	l2ReferralInfo, err := s.getReferralInfo(ctx, *referralInfo.ReferrerID)
	if err != nil || l2ReferralInfo.ReferrerID == nil {
		return uplineUsers, nil
	}

	l2User, err := s.getUserWithLevel(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil {
		return uplineUsers, nil
	}
	uplineUsers = append(uplineUsers, *l2User)

	// Get L3 upline (referrer of L2)
	l3ReferralInfo, err := s.getReferralInfo(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil || l3ReferralInfo.ReferrerID == nil {
		return uplineUsers, nil
	}

	l3User, err := s.getUserWithLevel(ctx, *l3ReferralInfo.ReferrerID)
	if err != nil {
		return uplineUsers, nil
	}
	uplineUsers = append(uplineUsers, *l3User)

	return uplineUsers, nil
}

// getReferralInfo gets referral information for a user
func (s *ContractCommissionService) getReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&referral).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

// getCommissionRateForDepth gets the commission rate for a specific depth based on the upline user's level
// This implements the commission matrix from the diagram
func (s *ContractCommissionService) getCommissionRateForDepth(ctx context.Context, uplineUser model.User, depth int) (decimal.Decimal, error) {
	switch depth {
	case 1: // Direct referral (L1)
		return uplineUser.AgentLevel.DirectCommissionRate, nil
	case 2: // Indirect referral (L2)
		return uplineUser.AgentLevel.IndirectCommissionRate, nil
	case 3: // Extended referral (L3)
		return uplineUser.AgentLevel.ExtendedCommissionRate, nil
	default:
		return decimal.Zero, nil
	}
}

// createCommissionLedger creates a commission ledger entry
func (s *ContractCommissionService) createCommissionLedger(ctx context.Context, tx *model.HyperLiquidTransaction, recipientUserID uuid.UUID) error {
	now := time.Now()

	commissionLedger := &model.CommissionLedger{
		RecipientUserID:       recipientUserID,
		SourceUserID:          *tx.UserID,
		SourceTransactionID:   tx.Cloid,
		SourceTransactionType: "CONTRACT",
		// CommissionAmount:      commissionAmount,
		CommissionAsset:       "USDC",
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	return global.GVA_DB.WithContext(ctx).Create(commissionLedger).Error
}

// createSelfRebateLedger creates a commission ledger entry for the user's self-rebate
func (s *ContractCommissionService) createSelfRebateLedger(ctx context.Context, tx *model.HyperLiquidTransaction, rebateAmount decimal.Decimal) error {
	now := time.Now()

	commissionLedger := &model.CommissionLedger{
		RecipientUserID:       *tx.UserID,
		SourceUserID:          *tx.UserID,
		SourceTransactionID:   tx.Cloid,
		SourceTransactionType: "CONTRACT_REBATE",
		CommissionAmount:      rebateAmount,
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	return global.GVA_DB.WithContext(ctx).Create(commissionLedger).Error
}
