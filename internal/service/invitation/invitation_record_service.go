package invitation

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/rebate"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// InvitationRecordService 邀请记录服务
type InvitationRecordService struct {
	userRepo        repo.InvitationRepo
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
}

// NewInvitationRecordService 创建邀请记录服务实例
func NewInvitationRecordService() *InvitationRecordService {
	return &InvitationRecordService{
		userRepo:        &rebate.InvitationRepository{},
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
	}
}

type InvitationRecord struct {
	AcceptedInvitationAddress string `json:"accepted_invitation_address"`
	AddressTransactionVolume decimal.Decimal `json:"address_transaction_volume"`
	SubordinateInvitedAddressCount int `json:"subordinate_invited_address_count"`
	AddressHandlingFee decimal.Decimal `json:"address_handling_fee"`
}

func (s *InvitationRecordService) GetInvitationRecords(ctx context.Context, userID uuid.UUID) ([]*InvitationRecord, error) {
	directReferrals, err := s.getDirectReferrals(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get direct referrals", zap.Error(err))
		return nil, fmt.Errorf("failed to get direct referrals: %w", err)
	}

	var records []*InvitationRecord

	for _, referral := range directReferrals {
		record, err := s.buildInvitationRecord(ctx, referral)
		if err != nil {
			global.GVA_LOG.Error("Failed to build invitation record",
				zap.String("user_id", referral.ID.String()),
				zap.Error(err))
			continue
		}
		records = append(records, record)
	}

	s.sortRecordsByVolume(records)

	return records, nil
}

func (s *InvitationRecordService) buildInvitationRecord(ctx context.Context, user *model.User) (*InvitationRecord, error) {
	walletAddress, err := s.getUserWalletAddress(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet address: %w", err)
	}

	transactionVolume, err := s.calculateTransactionVolume(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate transaction volume: %w", err)
	}

	subordinateCount, err := s.getSubordinateInvitedAddressCount(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subordinate count: %w", err)
	}

	handlingFee, err := s.calculateHandlingFee(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate handling fee: %w", err)
	}

	return &InvitationRecord{
		AcceptedInvitationAddress:      walletAddress,
		AddressTransactionVolume:       transactionVolume,
		SubordinateInvitedAddressCount: subordinateCount,
		AddressHandlingFee:             handlingFee,
	}, nil
}

func (s *InvitationRecordService) getDirectReferrals(ctx context.Context, userID uuid.UUID) ([]*model.User, error) {
	var referrals []model.Referral
	var users []*model.User

	err := global.GVA_DB.WithContext(ctx).
		Where("referrer_id = ? AND depth = 1", userID).
		Find(&referrals).Error

	if err != nil {
		return nil, err
	}

	if len(referrals) == 0 {
		return users, nil
	}

	var userIDs []uuid.UUID
	for _, referral := range referrals {
		userIDs = append(userIDs, referral.UserID)
	}

	err = global.GVA_DB.WithContext(ctx).
		Where("id IN ?", userIDs).
		Find(&users).Error

	if err != nil {
		return nil, err
	}

	return users, nil
}

func (s *InvitationRecordService) getUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	var walletAddress string
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ?", userID).
		Limit(1).
		Pluck("wallet_address", &walletAddress).Error

	if err != nil {
		return "", err
	}

	if walletAddress == "" {
		return "Unbound wallet", nil
	}

	return walletAddress, nil
}

func (s *InvitationRecordService) calculateTransactionVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	memeVolume, err := s.getMemeTransactionVolume(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}

	contractVolume, err := s.getContractTransactionVolume(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}

	return memeVolume.Add(contractVolume), nil
}

func (s *InvitationRecordService) getMemeTransactionVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(volume_usd), 0) as total_volume").
		Where("user_id = ? AND transaction_type = ? AND status = ?", userID, "MEME", model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalVolume, nil
}

func (s *InvitationRecordService) getContractTransactionVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.HyperLiquidTransaction{}).
		Select("COALESCE(SUM(avg_price * size), 0) as total_volume").
		Where("user_id = ? AND status = ?", userID, "completed").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalVolume, nil
}

func (s *InvitationRecordService) getSubordinateInvitedAddressCount(ctx context.Context, userID uuid.UUID) (int, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id = ? AND depth >= 1", userID).
		Count(&count).Error

	if err != nil {
		return 0, err
	}

	return int(count), nil
}

func (s *InvitationRecordService) calculateHandlingFee(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	memeFee, err := s.getMemeHandlingFee(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}

	contractFee, err := s.getContractHandlingFee(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}

	return memeFee.Add(contractFee), nil
}

func (s *InvitationRecordService) getMemeHandlingFee(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalFee decimal.Decimal `json:"total_fee"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(total_fee), 0) as total_fee").
		Where("user_id = ? AND transaction_type = ? AND status = ?", userID, "MEME", model.StatusCompleted).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, err
	}

	return result.TotalFee, nil
}

func (s *InvitationRecordService) getContractHandlingFee(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	return decimal.Zero, nil
}

func (s *InvitationRecordService) sortRecordsByVolume(records []*InvitationRecord) {
	for i := 0; i < len(records)-1; i++ {
		for j := 0; j < len(records)-i-1; j++ {
			if records[j].AddressTransactionVolume.LessThan(records[j+1].AddressTransactionVolume) {
				records[j], records[j+1] = records[j+1], records[j]
			}
		}
	}
}
